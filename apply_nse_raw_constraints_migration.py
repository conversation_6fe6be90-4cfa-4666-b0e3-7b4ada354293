#!/usr/bin/env python3
"""
Apply NSE raw tables unique constraints migration
"""

import sys
import psycopg2
from pathlib import Path
from psycopg2.extras import RealDictCursor

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.logging import get_logger
from src.core.config import get_settings

logger = get_logger(__name__)

def apply_migration():
    """Apply the NSE raw tables unique constraints migration."""
    try:
        settings = get_settings()
        db_url = settings.database.url
        
        logger.info("🔧 Starting NSE raw tables unique constraints migration...")
        
        # Read migration script
        migration_file = Path("src/database/migrations/add_nse_raw_unique_constraints.sql")
        if not migration_file.exists():
            logger.error(f"Migration file not found: {migration_file}")
            return False
        
        with open(migration_file, 'r') as f:
            migration_sql = f.read()
        
        # Connect to database
        conn = psycopg2.connect(db_url)
        
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Check current duplicate status before migration
                logger.info("📊 Checking current duplicate status...")
                
                # Check nse_cm_raw duplicates
                cursor.execute("""
                    SELECT COUNT(*) as total_rows,
                           COUNT(DISTINCT (fytoken, symbol_name, exchange_segment, instrument_id)) as unique_rows
                    FROM nse_cm_raw
                    WHERE fytoken IS NOT NULL 
                      AND symbol_name IS NOT NULL 
                      AND exchange_segment IS NOT NULL 
                      AND instrument_id IS NOT NULL
                """)
                cm_stats = cursor.fetchone()
                cm_duplicates = cm_stats['total_rows'] - cm_stats['unique_rows']
                
                # Check nse_fo_raw duplicates
                cursor.execute("""
                    SELECT COUNT(*) as total_rows,
                           COUNT(DISTINCT (fytoken, symbol_name, exchange_segment, instrument_id, expiry_timestamp)) as unique_rows
                    FROM nse_fo_raw
                    WHERE fytoken IS NOT NULL 
                      AND symbol_name IS NOT NULL 
                      AND exchange_segment IS NOT NULL 
                      AND instrument_id IS NOT NULL
                """)
                fo_stats = cursor.fetchone()
                fo_duplicates = fo_stats['total_rows'] - fo_stats['unique_rows']
                
                logger.info(f"nse_cm_raw: {cm_stats['total_rows']} total rows, {cm_duplicates} duplicates")
                logger.info(f"nse_fo_raw: {fo_stats['total_rows']} total rows, {fo_duplicates} duplicates")
                
                if cm_duplicates > 0 or fo_duplicates > 0:
                    logger.warning(f"Found duplicates that will be removed: CM={cm_duplicates}, FO={fo_duplicates}")
                else:
                    logger.info("✅ No duplicates found in NSE raw tables")
                
                # Apply migration
                logger.info("🔧 Applying migration...")
                cursor.execute(migration_sql)
                conn.commit()
                
                # Verify migration success
                logger.info("✅ Verifying migration success...")
                
                # Check if unique indexes were created
                cursor.execute("""
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE tablename IN ('nse_cm_raw', 'nse_fo_raw')
                      AND indexname LIKE '%unique%'
                    ORDER BY indexname
                """)
                unique_indexes = cursor.fetchall()
                
                logger.info("Created unique indexes:")
                for idx in unique_indexes:
                    logger.info(f"  ✅ {idx['indexname']}")
                
                # Final duplicate check
                cursor.execute("""
                    SELECT COUNT(*) as total_rows,
                           COUNT(DISTINCT (fytoken, symbol_name, exchange_segment, instrument_id)) as unique_rows
                    FROM nse_cm_raw
                    WHERE fytoken IS NOT NULL 
                      AND symbol_name IS NOT NULL 
                      AND exchange_segment IS NOT NULL 
                      AND instrument_id IS NOT NULL
                """)
                final_cm_stats = cursor.fetchone()
                final_cm_duplicates = final_cm_stats['total_rows'] - final_cm_stats['unique_rows']
                
                cursor.execute("""
                    SELECT COUNT(*) as total_rows,
                           COUNT(DISTINCT (fytoken, symbol_name, exchange_segment, instrument_id, expiry_timestamp)) as unique_rows
                    FROM nse_fo_raw
                    WHERE fytoken IS NOT NULL 
                      AND symbol_name IS NOT NULL 
                      AND exchange_segment IS NOT NULL 
                      AND instrument_id IS NOT NULL
                """)
                final_fo_stats = cursor.fetchone()
                final_fo_duplicates = final_fo_stats['total_rows'] - final_fo_stats['unique_rows']
                
                logger.info("📊 Post-migration status:")
                logger.info(f"nse_cm_raw: {final_cm_stats['total_rows']} total rows, {final_cm_duplicates} duplicates")
                logger.info(f"nse_fo_raw: {final_fo_stats['total_rows']} total rows, {final_fo_duplicates} duplicates")
                
                if final_cm_duplicates == 0 and final_fo_duplicates == 0:
                    logger.info("✅ Migration successful! No duplicates remain.")
                    return True
                else:
                    logger.error(f"❌ Migration failed! Duplicates still exist: CM={final_cm_duplicates}, FO={final_fo_duplicates}")
                    return False
                
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 NSE Raw Tables Unique Constraints Migration")
    logger.info("=" * 60)
    
    success = apply_migration()
    
    if success:
        logger.info("✅ Migration completed successfully!")
        logger.info("🔧 NSE raw tables now have unique constraints to prevent duplicates")
        logger.info("📝 Application will use UPSERT logic to handle duplicates gracefully")
    else:
        logger.error("❌ Migration failed!")
        sys.exit(1)
