#!/usr/bin/env python3
"""
Symbol classifier to automatically detect market type and construct proper Fyers symbols
"""

import re
from typing import Dict, Optional, Tuple
from src.database.models import MarketType
from src.core.logging import get_logger

logger = get_logger(__name__)

class SymbolClassifier:
    """Classify symbols and construct proper Fyers symbols for API calls."""
    
    def __init__(self):
        """Initialize the symbol classifier."""
        # Patterns for different market types
        self.patterns = {
            'EQUITY': re.compile(r'^([A-Z0-9&\-]+)-EQ$'),
            'INDEX': re.compile(r'^([A-Z0-9&\-]+)-INDEX$'),
            'FUTURES': re.compile(r'^([A-Z0-9&\-]+)(\d{2})([A-Z]{3})FUT$'),
            'OPTIONS_MONTHLY': re.compile(r'^([A-Z0-9&\-]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$'),
            'OPTIONS_WEEKLY': re.compile(r'^([A-Z0-9&\-]+)(\d{2})([1-9]\d{2})(\d+(?:\.\d+)?)(CE|PE)$')
        }
    
    def classify_symbol(self, symbol: str) -> Tuple[Optional[MarketType], Dict[str, any]]:
        """
        Classify a symbol and extract its components.
        
        Args:
            symbol: Symbol to classify (e.g., 'RELIANCE-EQ', 'NIFTY50-INDEX', 'RELIANCE25JULFUT')
            
        Returns:
            Tuple of (MarketType, symbol_info_dict)
        """
        symbol = symbol.strip().upper()
        
        # Remove NSE: prefix if present
        if symbol.startswith('NSE:'):
            symbol = symbol[4:]
        
        symbol_info = {
            'original_symbol': symbol,
            'underlying': None,
            'expiry_year': None,
            'expiry_month': None,
            'strike_price': None,
            'option_type': None,
            'fyers_symbol': None
        }
        
        # Check EQUITY pattern
        equity_match = self.patterns['EQUITY'].match(symbol)
        if equity_match:
            underlying = equity_match.group(1)
            symbol_info.update({
                'underlying': underlying,
                'fyers_symbol': f"NSE:{symbol}"
            })
            return MarketType.EQUITY, symbol_info
        
        # Check INDEX pattern
        index_match = self.patterns['INDEX'].match(symbol)
        if index_match:
            underlying = index_match.group(1)
            symbol_info.update({
                'underlying': underlying,
                'fyers_symbol': f"NSE:{symbol}"
            })
            return MarketType.INDEX, symbol_info
        
        # Check FUTURES pattern
        futures_match = self.patterns['FUTURES'].match(symbol)
        if futures_match:
            underlying = futures_match.group(1)
            year = futures_match.group(2)
            month = futures_match.group(3)
            symbol_info.update({
                'underlying': underlying,
                'expiry_year': year,
                'expiry_month': month,
                'fyers_symbol': f"NSE:{symbol}"
            })
            return MarketType.FUTURES, symbol_info
        
        # Check OPTIONS monthly pattern
        options_monthly_match = self.patterns['OPTIONS_MONTHLY'].match(symbol)
        if options_monthly_match:
            underlying = options_monthly_match.group(1)
            year = options_monthly_match.group(2)
            month = options_monthly_match.group(3)
            strike = float(options_monthly_match.group(4))
            option_type = options_monthly_match.group(5)
            symbol_info.update({
                'underlying': underlying,
                'expiry_year': year,
                'expiry_month': month,
                'strike_price': strike,
                'option_type': option_type,
                'fyers_symbol': f"NSE:{symbol}"
            })
            return MarketType.OPTIONS, symbol_info
        
        # Check OPTIONS weekly pattern
        options_weekly_match = self.patterns['OPTIONS_WEEKLY'].match(symbol)
        if options_weekly_match:
            underlying = options_weekly_match.group(1)
            year = options_weekly_match.group(2)
            mdd = options_weekly_match.group(3)  # month(1) + day(2)
            strike = float(options_weekly_match.group(4))
            option_type = options_weekly_match.group(5)
            symbol_info.update({
                'underlying': underlying,
                'expiry_year': year,
                'expiry_month': mdd[0],  # First digit is month
                'expiry_day': mdd[1:3],  # Next two digits are day
                'strike_price': strike,
                'option_type': option_type,
                'fyers_symbol': f"NSE:{symbol}"
            })
            return MarketType.OPTIONS, symbol_info
        
        # If no pattern matches, return None
        logger.warning(f"Could not classify symbol: {symbol}")
        return None, symbol_info
    
    def classify_symbols_batch(self, symbols: list) -> Dict[MarketType, list]:
        """
        Classify a batch of symbols and group them by market type.
        
        Args:
            symbols: List of symbols to classify
            
        Returns:
            Dictionary mapping MarketType to list of symbols
        """
        classified = {
            MarketType.EQUITY: [],
            MarketType.INDEX: [],
            MarketType.FUTURES: [],
            MarketType.OPTIONS: []
        }
        
        unclassified = []
        
        for symbol in symbols:
            market_type, symbol_info = self.classify_symbol(symbol)
            
            if market_type:
                classified[market_type].append({
                    'symbol': symbol,
                    'fyers_symbol': symbol_info['fyers_symbol'],
                    'info': symbol_info
                })
            else:
                unclassified.append(symbol)
        
        if unclassified:
            logger.warning(f"Could not classify {len(unclassified)} symbols: {unclassified}")
        
        return classified
    
    def get_fyers_symbol(self, symbol: str) -> Optional[str]:
        """
        Get the Fyers symbol for a given symbol.
        
        Args:
            symbol: Symbol to convert
            
        Returns:
            Fyers symbol or None if classification fails
        """
        market_type, symbol_info = self.classify_symbol(symbol)
        return symbol_info.get('fyers_symbol') if market_type else None
    
    def validate_test_symbols(self) -> Dict[str, Dict]:
        """
        Validate the test symbols mentioned in the requirements.
        
        Returns:
            Dictionary with validation results for each test symbol
        """
        test_symbols = {
            'RELIANCE-EQ': {'expected_type': MarketType.EQUITY, 'expected_fyers': 'NSE:RELIANCE-EQ'},
            'NIFTY50-INDEX': {'expected_type': MarketType.INDEX, 'expected_fyers': 'NSE:NIFTY50-INDEX'},
            'RELIANCE25JULFUT': {'expected_type': MarketType.FUTURES, 'expected_fyers': 'NSE:RELIANCE25JULFUT'},
            'NIFTY25JUL25000CE': {'expected_type': MarketType.OPTIONS, 'expected_fyers': 'NSE:NIFTY25JUL25000CE'}
        }
        
        results = {}
        
        for symbol, expected in test_symbols.items():
            market_type, symbol_info = self.classify_symbol(symbol)
            fyers_symbol = symbol_info.get('fyers_symbol')
            
            results[symbol] = {
                'classified_type': market_type,
                'expected_type': expected['expected_type'],
                'type_match': market_type == expected['expected_type'],
                'fyers_symbol': fyers_symbol,
                'expected_fyers': expected['expected_fyers'],
                'fyers_match': fyers_symbol == expected['expected_fyers'],
                'overall_success': (market_type == expected['expected_type'] and 
                                  fyers_symbol == expected['expected_fyers'])
            }
        
        return results

if __name__ == "__main__":
    # Test the classifier
    classifier = SymbolClassifier()
    
    print("🧪 Testing Symbol Classifier")
    print("=" * 50)
    
    # Test individual symbols
    test_symbols = [
        'RELIANCE-EQ',
        'NIFTY50-INDEX', 
        'RELIANCE25JULFUT',
        'NIFTY25JUL25000CE',
        'M&M-EQ',
        'BAJAJ-AUTO-EQ'
    ]
    
    for symbol in test_symbols:
        market_type, symbol_info = classifier.classify_symbol(symbol)
        print(f"{symbol:20} -> {market_type.value if market_type else 'UNKNOWN':10} | {symbol_info.get('fyers_symbol', 'N/A')}")
    
    # Test batch classification
    print("\n🔄 Testing Batch Classification")
    print("-" * 30)
    classified = classifier.classify_symbols_batch(test_symbols)
    
    for market_type, symbols in classified.items():
        if symbols:
            print(f"{market_type.value}: {len(symbols)} symbols")
            for sym_info in symbols:
                print(f"  - {sym_info['symbol']} -> {sym_info['fyers_symbol']}")
    
    # Test validation
    print("\n✅ Testing Validation")
    print("-" * 20)
    validation_results = classifier.validate_test_symbols()
    
    for symbol, result in validation_results.items():
        status = "✅" if result['overall_success'] else "❌"
        print(f"{status} {symbol}: {result['classified_type'].value if result['classified_type'] else 'UNKNOWN'}")
