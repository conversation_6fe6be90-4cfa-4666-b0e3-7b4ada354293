#!/usr/bin/env python3
"""
Test enhanced pattern matching for symbols with special characters
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.nse_symbol_helpers import NSESymbolHelpers
from src.core.universal_symbol_parser import UniversalSymbolParser
from src.core.logging import get_logger

logger = get_logger(__name__)

def test_special_character_patterns():
    """Test pattern matching for symbols with special characters like M&M and BAJAJ-AUTO."""
    
    logger.info("🧪 Testing enhanced pattern matching for special characters...")
    
    helpers = NSESymbolHelpers()
    # Skip UniversalSymbolParser test for now as it requires config
    # parser = UniversalSymbolParser()
    
    # Test symbols with special characters
    test_symbols = {
        'EQUITY': [
            'RELIANCE-EQ',      # Standard equity
            'TCS-EQ',           # Standard equity
            'M&M-EQ',           # Equity with ampersand
            'BAJAJ-AUTO-EQ',    # Equity with hyphen
            'L&T-EQ',           # Equity with ampersand and hyphen
            'HDFCBANK-EQ',      # Standard equity
        ],
        'INDEX': [
            'NIFTY50-INDEX',    # Standard index
            'BANKNIFTY-INDEX',  # Standard index
            'NIFTYIT-INDEX',    # Standard index
        ],
        'FUTURES': [
            'RELIANCE25JULFUT',     # Standard futures
            'M&M25JULFUT',          # Futures with ampersand
            'BAJAJ-AUTO25JULFUT',   # Futures with hyphen
            'L&T25JULFUT',          # Futures with ampersand and hyphen
            'NIFTY25JULFUT',        # Index futures
        ],
        'OPTIONS_MONTHLY': [
            'RELIANCE25JUL2500CE',      # Standard options
            'M&M25JUL1500CE',           # Options with ampersand
            'BAJAJ-AUTO25JUL5900CE',    # Options with hyphen
            'L&T25JUL2000PE',           # Options with ampersand and hyphen
            'NIFTY25JUL25000CE',        # Index options
        ],
        'OPTIONS_WEEKLY': [
            'NIFTY2572425050CE',        # Weekly options
            'BANKNIFTY2572450000PE',    # Weekly options
            'FINNIFTY2572420000CE',     # Weekly options
        ]
    }
    
    # Test with NSESymbolHelpers
    logger.info("\n📊 Testing NSESymbolHelpers pattern matching:")
    logger.info("-" * 50)
    
    total_tests = 0
    passed_tests = 0
    
    for expected_type, symbols in test_symbols.items():
        logger.info(f"\n{expected_type} symbols:")
        for symbol in symbols:
            total_tests += 1
            symbol_info = helpers.extract_symbol_info(symbol)
            detected_type = symbol_info.get('market_type')
            
            # For options, both monthly and weekly should return 'OPTIONS'
            expected_check = expected_type
            if expected_type in ['OPTIONS_MONTHLY', 'OPTIONS_WEEKLY']:
                expected_check = 'OPTIONS'

            if detected_type == expected_check:
                logger.info(f"  ✅ {symbol} -> {detected_type}")
                passed_tests += 1
            else:
                logger.error(f"  ❌ {symbol} -> {detected_type} (expected {expected_check})")
    
    # Skip UniversalSymbolParser test for now
    logger.info("\n📊 Skipping UniversalSymbolParser test (requires config)")
    logger.info("-" * 50)
    
    # Summary
    logger.info("\n📊 Test Summary:")
    logger.info("-" * 30)
    logger.info(f"Total tests: {total_tests}")
    logger.info(f"Passed: {passed_tests}")
    logger.info(f"Failed: {total_tests - passed_tests}")
    logger.info(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
    
    return passed_tests == total_tests

def test_edge_cases():
    """Test edge cases and boundary conditions."""
    
    logger.info("\n🔬 Testing edge cases...")
    logger.info("-" * 30)
    
    helpers = NSESymbolHelpers()
    
    # Edge cases that should NOT match
    invalid_symbols = [
        'RELIANCE',         # Missing suffix
        'RELIANCE-',        # Incomplete suffix
        'RELIANCE-EQX',     # Wrong suffix
        'M&M25JUL',         # Incomplete futures
        'BAJAJ-AUTO25',     # Incomplete futures
        'NIFTY2572425050',  # Missing CE/PE
        '',                 # Empty string
        'NSE:RELIANCE-EQ',  # With exchange prefix (should be handled by parser)
    ]
    
    logger.info("Testing invalid symbols (should not match):")
    for symbol in invalid_symbols:
        symbol_info = helpers.extract_symbol_info(symbol)
        market_type = symbol_info.get('market_type')
        
        if market_type is None:
            logger.info(f"  ✅ {symbol} -> None (correctly rejected)")
        else:
            logger.error(f"  ❌ {symbol} -> {market_type} (should be None)")
    
    # Test symbols with multiple special characters
    complex_symbols = [
        'L&T-EQ',               # Both & and -
        'L&T25JULFUT',          # Both & and - in futures
        'L&T25JUL2000CE',       # Both & and - in options
    ]
    
    logger.info("\nTesting complex symbols with multiple special characters:")
    for symbol in complex_symbols:
        symbol_info = helpers.extract_symbol_info(symbol)
        market_type = symbol_info.get('market_type')
        underlying = symbol_info.get('underlying')
        
        if market_type:
            logger.info(f"  ✅ {symbol} -> {market_type} (underlying: {underlying})")
        else:
            logger.error(f"  ❌ {symbol} -> None (should match)")

if __name__ == "__main__":
    logger.info("🚀 Enhanced Pattern Matching Test Suite")
    logger.info("=" * 60)
    
    # Test 1: Special character patterns
    pattern_success = test_special_character_patterns()
    
    # Test 2: Edge cases
    test_edge_cases()
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("🏁 FINAL RESULTS")
    logger.info("=" * 60)
    
    logger.info(f"Pattern matching: {'✅ PASS' if pattern_success else '❌ FAIL'}")
    
    if pattern_success:
        logger.info("✅ All pattern matching tests passed!")
        logger.info("🔧 Enhanced patterns now support symbols with special characters:")
        logger.info("   - M&M (ampersand)")
        logger.info("   - BAJAJ-AUTO (hyphen)")
        logger.info("   - L&T (both ampersand and hyphen)")
    else:
        logger.error("❌ Some pattern matching tests failed!")
        
    sys.exit(0 if pattern_success else 1)
