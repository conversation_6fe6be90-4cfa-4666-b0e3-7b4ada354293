# Usage Examples for Enhanced Data Service

This document provides examples of how to use the new functionality added to the data service.

## 1. Fix Fyers Symbol Issues

### Fix null fyers_symbol values using exact examples
```bash
python main.py --fix-fyers-symbols
```

### Fix all market type tables comprehensively
```bash
python main.py --fix-market-type-tables
```

### Fix all market type tables without backup (use with caution)
```bash
python main.py --fix-market-type-tables --no-backup
```

## 2. Fetch Data for Specific Symbols

### Fetch data for specific equity symbol
```bash
python main.py --fetch-equity "NSE:RELIANCE-EQ" --days 1
```

### Fetch data for specific index symbol
```bash
python main.py --fetch-index "NSE:NIFTY50-INDEX" --days 1
```

### Fetch data for specific futures symbol
```bash
python main.py --fetch-futures "NSE:RELIANCE25JULFUT" --days 1
```

### Fetch data for specific options symbol
```bash
python main.py --fetch-options "NSE:NIFTY25JUL25000CE" --days 1
```

## 3. Automatic Symbol Processing

### Process all equity symbols from symbol_mapping table
```bash
python main.py --auto-all-symbols --market-type EQUITY --days 1
```

### Process all index symbols with limit (for testing)
```bash
python main.py --auto-all-symbols --market-type INDEX --days 1 --limit 10
```

### Process futures symbols with resume capability
```bash
python main.py --auto-all-symbols --market-type FUTURES --days 1 --resume-from 50
```

### Process options symbols
```bash
python main.py --auto-all-symbols --market-type OPTIONS --days 1
```

## 4. Data Management Operations

### Comprehensive data health report
```bash
python main.py --data-health-report
```

### Validate data integrity
```bash
python main.py --validate-data-integrity
```

### View data availability summary
```bash
python main.py --view-data
```

## 5. Testing and Validation

### Run the test script
```bash
python test_new_functionality.py
```

### Process NSE symbols and fix issues
```bash
python main.py --process-nse-symbols
```

## 6. Complete Workflow Examples

### Complete setup and data fetch for all market types
```bash
# 1. Initialize database
python main.py --init-db

# 2. Process NSE symbols
python main.py --process-nse-symbols

# 3. Fix fyers symbols
python main.py --fix-fyers-symbols

# 4. Fetch sample data for each market type
python main.py --fetch-equity "NSE:RELIANCE-EQ" --days 1
python main.py --fetch-index "NSE:NIFTY50-INDEX" --days 1
python main.py --fetch-futures "NSE:RELIANCE25JULFUT" --days 1
python main.py --fetch-options "NSE:NIFTY25JUL25000CE" --days 1

# 5. Validate everything
python main.py --validate-data-integrity
```

### Bulk processing with error handling
```bash
# Process all equity symbols with resume capability
python main.py --auto-all-symbols --market-type EQUITY --days 1 --limit 100

# If it fails at symbol 50, resume from there
python main.py --auto-all-symbols --market-type EQUITY --days 1 --resume-from 50
```

## 7. Exact Symbols Used (As Specified by User)

The system now supports these exact Fyers symbol patterns:

- **EQUITY**: `NSE:RELIANCE-EQ`
- **INDEX**: `NSE:NIFTY50-INDEX`
- **FUTURES**: `NSE:RELIANCE25JULFUT`
- **OPTIONS**: `NSE:NIFTY25JUL25000CE`

## 8. Error Recovery

### If processing fails, check table status
```bash
python main.py --data-health-report
```

### Fix any issues found
```bash
python main.py --fix-market-type-tables
```

### Resume processing from where it left off
```bash
python main.py --auto-all-symbols --market-type EQUITY --resume-from LAST_INDEX
```

## 9. Monitoring and Maintenance

### Regular health check
```bash
python main.py --data-health-report
```

### Fix any data issues
```bash
python main.py --fix-all-data-issues
```

### Remove duplicates if needed
```bash
python main.py --remove-duplicates
```

## Notes

1. All operations now support the exact Fyers symbol formats specified by the user
2. The system automatically handles symbol mapping and construction
3. Error recovery and resume functionality is built-in
4. All market type tables (equity_ohlcv, index_ohlcv, futures_ohlcv, options_ohlcv) are supported
5. The data management service now works with all tables, not just raw NSE tables
