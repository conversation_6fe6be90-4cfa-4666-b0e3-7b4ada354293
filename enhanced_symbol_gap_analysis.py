#!/usr/bin/env python3
"""
Enhanced symbol gap analysis to identify why symbols are being dropped
and provide detailed breakdown of missing symbols
"""

import sys
import pandas as pd
from pathlib import Path
from collections import defaultdict, Counter
import re

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.nse_symbol_processor import NSESymbolProcessor
from src.core.logging import get_logger
from datetime import datetime

logger = get_logger(__name__)

def analyze_skipped_symbols_detailed():
    """Analyze skipped symbols in detail to understand why they're being dropped."""
    
    processor = NSESymbolProcessor()
    
    # Analyze NSE_CM.csv
    nse_cm_path = Path("NSE_CM.csv")
    if not nse_cm_path.exists():
        logger.error("NSE_CM.csv not found!")
        return None
    
    logger.info("📊 Analyzing NSE_CM.csv for skipped symbols...")
    df = processor._parse_csv_file(nse_cm_path, processor.nse_cm_columns)
    
    skipped_analysis = {
        'total_rows': len(df),
        'valid_symbols': 0,
        'skipped_symbols': 0,
        'skipped_categories': defaultdict(list),
        'skipped_patterns': Counter(),
        'equity_like': [],
        'index_like': [],
        'other_patterns': []
    }
    
    for _, row in df.iterrows():
        fyers_symbol = str(row.get('symbol_name', '')).strip()
        
        if not fyers_symbol.startswith('NSE:'):
            skipped_analysis['skipped_symbols'] += 1
            skipped_analysis['skipped_categories']['no_nse_prefix'].append(fyers_symbol)
            continue
            
        nse_symbol = fyers_symbol[4:]  # Remove 'NSE:' prefix
        symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
        
        if symbol_info['market_type'] in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
            skipped_analysis['valid_symbols'] += 1
        else:
            skipped_analysis['skipped_symbols'] += 1
            
            # Categorize skipped symbols
            if '-' in nse_symbol:
                suffix = nse_symbol.split('-')[-1]
                skipped_analysis['skipped_patterns'][f"suffix_{suffix}"] += 1
                
                if suffix in ['SG', 'GS', 'MF', 'BE', 'YL']:
                    skipped_analysis['skipped_categories']['government_bonds_mf'].append(nse_symbol)
                elif suffix in ['SM', 'BZ', 'IL']:
                    skipped_analysis['skipped_categories']['sme_bonds'].append(nse_symbol)
                elif len(suffix) > 5:
                    skipped_analysis['skipped_categories']['long_suffix'].append(nse_symbol)
                else:
                    skipped_analysis['skipped_categories']['other_suffix'].append(nse_symbol)
            else:
                # No suffix - might be incomplete or different format
                if nse_symbol.endswith('EQ') and not nse_symbol.endswith('-EQ'):
                    skipped_analysis['equity_like'].append(nse_symbol)
                elif 'INDEX' in nse_symbol and not nse_symbol.endswith('-INDEX'):
                    skipped_analysis['index_like'].append(nse_symbol)
                else:
                    skipped_analysis['other_patterns'].append(nse_symbol)
                    
                skipped_analysis['skipped_patterns']['no_suffix'] += 1
    
    return skipped_analysis

def analyze_expected_vs_actual():
    """Compare expected vs actual symbol counts and identify gaps."""
    
    logger.info("📊 Analyzing expected vs actual symbol counts...")
    
    # Expected counts (from user requirements)
    expected_counts = {
        'EQUITY': 2158,
        'INDEX': 124,
        'FUTURES': 'TBD',  # Will be determined from analysis
        'OPTIONS': 'TBD'   # Will be determined from analysis
    }
    
    # Run the existing analysis
    processor = NSESymbolProcessor()
    
    # Analyze NSE_CM.csv
    nse_cm_path = Path("NSE_CM.csv")
    nse_cm_results = {'EQUITY': 0, 'INDEX': 0, 'FUTURES': 0, 'OPTIONS': 0}
    
    if nse_cm_path.exists():
        df = processor._parse_csv_file(nse_cm_path, processor.nse_cm_columns)
        for _, row in df.iterrows():
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            if fyers_symbol.startswith('NSE:'):
                nse_symbol = fyers_symbol[4:]
                symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
                market_type = symbol_info.get('market_type')
                if market_type in nse_cm_results:
                    nse_cm_results[market_type] += 1
    
    # Analyze NSE_FO.csv
    nse_fo_path = Path("NSE_FO.csv")
    nse_fo_results = {'EQUITY': 0, 'INDEX': 0, 'FUTURES': 0, 'OPTIONS': 0}
    
    if nse_fo_path.exists():
        df = processor._parse_csv_file(nse_fo_path, processor.nse_fo_columns)
        for _, row in df.iterrows():
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            if fyers_symbol.startswith('NSE:'):
                nse_symbol = fyers_symbol[4:]
                symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
                market_type = symbol_info.get('market_type')
                if market_type in nse_fo_results:
                    nse_fo_results[market_type] += 1
    
    # Combine results
    actual_counts = {
        'EQUITY': nse_cm_results['EQUITY'] + nse_fo_results['EQUITY'],
        'INDEX': nse_cm_results['INDEX'] + nse_fo_results['INDEX'],
        'FUTURES': nse_cm_results['FUTURES'] + nse_fo_results['FUTURES'],
        'OPTIONS': nse_cm_results['OPTIONS'] + nse_fo_results['OPTIONS']
    }
    
    # Calculate gaps
    gaps = {}
    for market_type in ['EQUITY', 'INDEX']:
        expected = expected_counts[market_type]
        actual = actual_counts[market_type]
        gaps[market_type] = {
            'expected': expected,
            'actual': actual,
            'gap': expected - actual,
            'percentage': (actual / expected) * 100 if expected > 0 else 0
        }
    
    return gaps, actual_counts

def generate_detailed_gap_report():
    """Generate a detailed gap analysis report."""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = Path("logs") / f"detailed_gap_analysis_{timestamp}.txt"
    
    # Ensure logs directory exists
    report_file.parent.mkdir(exist_ok=True)
    
    # Get analysis data
    skipped_analysis = analyze_skipped_symbols_detailed()
    gaps, actual_counts = analyze_expected_vs_actual()
    
    with open(report_file, 'w') as f:
        f.write("DETAILED SYMBOL GAP ANALYSIS REPORT\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {datetime.now()}\n\n")
        
        # Summary
        f.write("EXECUTIVE SUMMARY\n")
        f.write("-" * 40 + "\n")
        f.write(f"Total NSE_CM.csv rows: {skipped_analysis['total_rows']:,}\n")
        f.write(f"Valid symbols processed: {skipped_analysis['valid_symbols']:,}\n")
        f.write(f"Symbols skipped: {skipped_analysis['skipped_symbols']:,}\n")
        f.write(f"Skip rate: {(skipped_analysis['skipped_symbols']/skipped_analysis['total_rows'])*100:.1f}%\n\n")
        
        # Expected vs Actual
        f.write("EXPECTED VS ACTUAL COUNTS\n")
        f.write("-" * 40 + "\n")
        for market_type, gap_info in gaps.items():
            f.write(f"{market_type}:\n")
            f.write(f"  Expected: {gap_info['expected']:,}\n")
            f.write(f"  Actual: {gap_info['actual']:,}\n")
            f.write(f"  Gap: {gap_info['gap']:,}\n")
            f.write(f"  Coverage: {gap_info['percentage']:.1f}%\n\n")
        
        # Skipped symbol categories
        f.write("SKIPPED SYMBOL CATEGORIES\n")
        f.write("-" * 40 + "\n")
        
        f.write("Government Bonds/Mutual Funds (SG, GS, MF, BE, YL suffixes):\n")
        gov_bonds = skipped_analysis['skipped_categories']['government_bonds_mf']
        f.write(f"  Count: {len(gov_bonds)}\n")
        f.write(f"  Sample: {', '.join(gov_bonds[:10])}\n\n")
        
        f.write("SME/Other Bonds (SM, BZ, IL suffixes):\n")
        sme_bonds = skipped_analysis['skipped_categories']['sme_bonds']
        f.write(f"  Count: {len(sme_bonds)}\n")
        f.write(f"  Sample: {', '.join(sme_bonds[:10])}\n\n")
        
        f.write("Symbols without NSE: prefix:\n")
        no_prefix = skipped_analysis['skipped_categories']['no_nse_prefix']
        f.write(f"  Count: {len(no_prefix)}\n")
        f.write(f"  Sample: {', '.join(no_prefix[:10])}\n\n")
        
        f.write("Other suffix patterns:\n")
        other_suffix = skipped_analysis['skipped_categories']['other_suffix']
        f.write(f"  Count: {len(other_suffix)}\n")
        f.write(f"  Sample: {', '.join(other_suffix[:10])}\n\n")
        
        # Pattern frequency
        f.write("SKIPPED PATTERN FREQUENCY\n")
        f.write("-" * 40 + "\n")
        for pattern, count in skipped_analysis['skipped_patterns'].most_common(20):
            f.write(f"  {pattern}: {count:,}\n")
        
        # Recommendations
        f.write("\nRECOMMENDATIONS\n")
        f.write("-" * 40 + "\n")
        f.write("1. Government bonds, mutual funds, and SME instruments are correctly excluded\n")
        f.write("2. Focus on finding missing EQUITY and INDEX symbols\n")
        f.write("3. Check if some valid symbols have non-standard formats\n")
        f.write("4. Verify if expected counts include delisted/inactive symbols\n")
    
    logger.info(f"📄 Detailed gap analysis written to: {report_file}")
    return report_file

if __name__ == "__main__":
    logger.info("🔍 Enhanced Symbol Gap Analysis")
    logger.info("=" * 60)
    
    try:
        report_file = generate_detailed_gap_report()
        
        # Also run the basic analysis for comparison
        gaps, actual_counts = analyze_expected_vs_actual()
        
        logger.info("\n📊 SUMMARY:")
        logger.info(f"Actual symbol counts:")
        for market_type, count in actual_counts.items():
            logger.info(f"  {market_type}: {count:,}")
        
        logger.info(f"\nGap analysis:")
        for market_type, gap_info in gaps.items():
            logger.info(f"  {market_type}: {gap_info['actual']:,}/{gap_info['expected']:,} ({gap_info['percentage']:.1f}%)")
        
        logger.info(f"\n✅ Detailed analysis completed! Report: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        sys.exit(1)
