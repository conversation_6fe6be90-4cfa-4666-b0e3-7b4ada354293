#!/usr/bin/env python3
"""
Identify specific missing symbols by analyzing patterns and formats
"""

import sys
import pandas as pd
from pathlib import Path
from collections import defaultdict
import re

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.nse_symbol_processor import NSESymbolProcessor
from src.core.logging import get_logger
from datetime import datetime

logger = get_logger(__name__)

def extract_all_symbols_from_csv():
    """Extract all symbols from CSV files and categorize them."""
    
    processor = NSESymbolProcessor()
    
    all_symbols = {
        'NSE_CM': {
            'valid_equity': [],
            'valid_index': [],
            'potential_equity': [],  # Symbols that might be equity but don't match pattern
            'potential_index': [],   # Symbols that might be index but don't match pattern
            'skipped': []
        },
        'NSE_FO': {
            'valid_futures': [],
            'valid_options': [],
            'potential_futures': [],
            'potential_options': [],
            'skipped': []
        }
    }
    
    # Analyze NSE_CM.csv
    nse_cm_path = Path("NSE_CM.csv")
    if nse_cm_path.exists():
        logger.info("📊 Analyzing NSE_CM.csv for all symbol patterns...")
        df = processor._parse_csv_file(nse_cm_path, processor.nse_cm_columns)
        
        for _, row in df.iterrows():
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            
            if not fyers_symbol.startswith('NSE:'):
                continue
                
            nse_symbol = fyers_symbol[4:]
            symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
            
            # Get additional info from CSV
            instrument_type = str(row.get('instrument_type', '')).strip()
            
            if symbol_info['market_type'] == 'EQUITY':
                all_symbols['NSE_CM']['valid_equity'].append({
                    'symbol': nse_symbol,
                    'fyers_symbol': fyers_symbol,
                    'instrument_type': instrument_type
                })
            elif symbol_info['market_type'] == 'INDEX':
                all_symbols['NSE_CM']['valid_index'].append({
                    'symbol': nse_symbol,
                    'fyers_symbol': fyers_symbol,
                    'instrument_type': instrument_type
                })
            else:
                # Check if it might be equity/index with non-standard format
                if instrument_type == 'EQ' or 'EQ' in nse_symbol:
                    all_symbols['NSE_CM']['potential_equity'].append({
                        'symbol': nse_symbol,
                        'fyers_symbol': fyers_symbol,
                        'instrument_type': instrument_type,
                        'reason': 'instrument_type_eq_but_no_pattern_match'
                    })
                elif instrument_type == 'INDEX' or 'INDEX' in nse_symbol or 'NIFTY' in nse_symbol:
                    all_symbols['NSE_CM']['potential_index'].append({
                        'symbol': nse_symbol,
                        'fyers_symbol': fyers_symbol,
                        'instrument_type': instrument_type,
                        'reason': 'instrument_type_index_but_no_pattern_match'
                    })
                else:
                    all_symbols['NSE_CM']['skipped'].append({
                        'symbol': nse_symbol,
                        'fyers_symbol': fyers_symbol,
                        'instrument_type': instrument_type
                    })
    
    # Analyze NSE_FO.csv
    nse_fo_path = Path("NSE_FO.csv")
    if nse_fo_path.exists():
        logger.info("📊 Analyzing NSE_FO.csv for all symbol patterns...")
        df = processor._parse_csv_file(nse_fo_path, processor.nse_fo_columns)
        
        for _, row in df.iterrows():
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            
            if not fyers_symbol.startswith('NSE:'):
                continue
                
            nse_symbol = fyers_symbol[4:]
            symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
            
            # Get additional info from CSV
            instrument_type = str(row.get('instrument_type', '')).strip()
            
            if symbol_info['market_type'] == 'FUTURES':
                all_symbols['NSE_FO']['valid_futures'].append({
                    'symbol': nse_symbol,
                    'fyers_symbol': fyers_symbol,
                    'instrument_type': instrument_type
                })
            elif symbol_info['market_type'] == 'OPTIONS':
                all_symbols['NSE_FO']['valid_options'].append({
                    'symbol': nse_symbol,
                    'fyers_symbol': fyers_symbol,
                    'instrument_type': instrument_type
                })
            else:
                # Check if it might be futures/options with non-standard format
                if instrument_type == 'FUT' or 'FUT' in nse_symbol:
                    all_symbols['NSE_FO']['potential_futures'].append({
                        'symbol': nse_symbol,
                        'fyers_symbol': fyers_symbol,
                        'instrument_type': instrument_type,
                        'reason': 'instrument_type_fut_but_no_pattern_match'
                    })
                elif instrument_type in ['CE', 'PE'] or 'CE' in nse_symbol or 'PE' in nse_symbol:
                    all_symbols['NSE_FO']['potential_options'].append({
                        'symbol': nse_symbol,
                        'fyers_symbol': fyers_symbol,
                        'instrument_type': instrument_type,
                        'reason': 'instrument_type_option_but_no_pattern_match'
                    })
                else:
                    all_symbols['NSE_FO']['skipped'].append({
                        'symbol': nse_symbol,
                        'fyers_symbol': fyers_symbol,
                        'instrument_type': instrument_type
                    })
    
    return all_symbols

def analyze_potential_missing_symbols(all_symbols):
    """Analyze potential missing symbols that might be valid but don't match patterns."""
    
    analysis = {
        'equity_analysis': {
            'valid_count': len(all_symbols['NSE_CM']['valid_equity']),
            'potential_count': len(all_symbols['NSE_CM']['potential_equity']),
            'potential_samples': all_symbols['NSE_CM']['potential_equity'][:20]
        },
        'index_analysis': {
            'valid_count': len(all_symbols['NSE_CM']['valid_index']),
            'potential_count': len(all_symbols['NSE_CM']['potential_index']),
            'potential_samples': all_symbols['NSE_CM']['potential_index'][:20]
        },
        'futures_analysis': {
            'valid_count': len(all_symbols['NSE_FO']['valid_futures']),
            'potential_count': len(all_symbols['NSE_FO']['potential_futures']),
            'potential_samples': all_symbols['NSE_FO']['potential_futures'][:20]
        },
        'options_analysis': {
            'valid_count': len(all_symbols['NSE_FO']['valid_options']),
            'potential_count': len(all_symbols['NSE_FO']['potential_options']),
            'potential_samples': all_symbols['NSE_FO']['potential_options'][:20]
        }
    }
    
    return analysis

def generate_missing_symbols_report():
    """Generate a comprehensive report of missing symbols."""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = Path("logs") / f"missing_symbols_analysis_{timestamp}.txt"
    
    # Ensure logs directory exists
    report_file.parent.mkdir(exist_ok=True)
    
    # Get analysis data
    all_symbols = extract_all_symbols_from_csv()
    potential_analysis = analyze_potential_missing_symbols(all_symbols)
    
    with open(report_file, 'w') as f:
        f.write("MISSING SYMBOLS ANALYSIS REPORT\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {datetime.now()}\n\n")
        
        # Summary
        f.write("SYMBOL COUNTS SUMMARY\n")
        f.write("-" * 40 + "\n")
        f.write(f"EQUITY - Valid: {potential_analysis['equity_analysis']['valid_count']}, Potential: {potential_analysis['equity_analysis']['potential_count']}\n")
        f.write(f"INDEX - Valid: {potential_analysis['index_analysis']['valid_count']}, Potential: {potential_analysis['index_analysis']['potential_count']}\n")
        f.write(f"FUTURES - Valid: {potential_analysis['futures_analysis']['valid_count']}, Potential: {potential_analysis['futures_analysis']['potential_count']}\n")
        f.write(f"OPTIONS - Valid: {potential_analysis['options_analysis']['valid_count']}, Potential: {potential_analysis['options_analysis']['potential_count']}\n\n")
        
        # Gap analysis
        f.write("GAP ANALYSIS\n")
        f.write("-" * 40 + "\n")
        equity_total = potential_analysis['equity_analysis']['valid_count'] + potential_analysis['equity_analysis']['potential_count']
        index_total = potential_analysis['index_analysis']['valid_count'] + potential_analysis['index_analysis']['potential_count']
        
        f.write(f"EQUITY: {potential_analysis['equity_analysis']['valid_count']} valid + {potential_analysis['equity_analysis']['potential_count']} potential = {equity_total} total\n")
        f.write(f"  Expected: 2,158, Gap if all potential are valid: {2158 - equity_total}\n")
        f.write(f"INDEX: {potential_analysis['index_analysis']['valid_count']} valid + {potential_analysis['index_analysis']['potential_count']} potential = {index_total} total\n")
        f.write(f"  Expected: 124, Gap if all potential are valid: {124 - index_total}\n\n")
        
        # Potential equity symbols
        f.write("POTENTIAL EQUITY SYMBOLS (Sample)\n")
        f.write("-" * 40 + "\n")
        for i, symbol_info in enumerate(potential_analysis['equity_analysis']['potential_samples'], 1):
            f.write(f"{i:2d}. {symbol_info['symbol']} (instrument_type: {symbol_info['instrument_type']})\n")
        f.write(f"\n")
        
        # Potential index symbols
        f.write("POTENTIAL INDEX SYMBOLS (Sample)\n")
        f.write("-" * 40 + "\n")
        for i, symbol_info in enumerate(potential_analysis['index_analysis']['potential_samples'], 1):
            f.write(f"{i:2d}. {symbol_info['symbol']} (instrument_type: {symbol_info['instrument_type']})\n")
        f.write(f"\n")
        
        # Recommendations
        f.write("RECOMMENDATIONS\n")
        f.write("-" * 40 + "\n")
        f.write("1. Review potential equity symbols with instrument_type='EQ' but non-standard format\n")
        f.write("2. Review potential index symbols with instrument_type='INDEX' but non-standard format\n")
        f.write("3. Consider if pattern matching rules need adjustment for edge cases\n")
        f.write("4. Verify if expected counts include delisted/suspended symbols\n")
        f.write("5. Check if some symbols use different naming conventions\n")
    
    logger.info(f"📄 Missing symbols analysis written to: {report_file}")
    return report_file

if __name__ == "__main__":
    logger.info("🔍 Missing Symbols Analysis")
    logger.info("=" * 60)
    
    try:
        report_file = generate_missing_symbols_report()
        
        # Quick summary
        all_symbols = extract_all_symbols_from_csv()
        potential_analysis = analyze_potential_missing_symbols(all_symbols)
        
        logger.info("\n📊 SUMMARY:")
        logger.info(f"EQUITY: {potential_analysis['equity_analysis']['valid_count']} valid, {potential_analysis['equity_analysis']['potential_count']} potential")
        logger.info(f"INDEX: {potential_analysis['index_analysis']['valid_count']} valid, {potential_analysis['index_analysis']['potential_count']} potential")
        logger.info(f"FUTURES: {potential_analysis['futures_analysis']['valid_count']} valid, {potential_analysis['futures_analysis']['potential_count']} potential")
        logger.info(f"OPTIONS: {potential_analysis['options_analysis']['valid_count']} valid, {potential_analysis['options_analysis']['potential_count']} potential")
        
        logger.info(f"\n✅ Missing symbols analysis completed! Report: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        sys.exit(1)
