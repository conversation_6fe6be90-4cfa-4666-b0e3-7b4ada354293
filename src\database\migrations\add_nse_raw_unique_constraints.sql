-- Migration script to add unique constraints to NSE raw tables
-- This script handles existing duplicate data and adds unique constraints

-- Step 1: Remove any existing duplicates from nse_cm_raw
-- Keep only the first occurrence (lowest ID) for each unique combination
WITH duplicates AS (
    SELECT 
        fytoken, symbol_name, exchange_segment, instrument_id,
        MIN(id) as keep_id,
        ARRAY_AGG(id ORDER BY id) as all_ids
    FROM nse_cm_raw
    WHERE fytoken IS NOT NULL 
      AND symbol_name IS NOT NULL 
      AND exchange_segment IS NOT NULL 
      AND instrument_id IS NOT NULL
    GROUP BY fytoken, symbol_name, exchange_segment, instrument_id
    HAVING COUNT(*) > 1
)
DELETE FROM nse_cm_raw 
WHERE id IN (
    SELECT UNNEST(all_ids[2:]) 
    FROM duplicates
);

-- Step 2: Remove any existing duplicates from nse_fo_raw
-- Keep only the first occurrence (lowest ID) for each unique combination
WITH duplicates AS (
    SELECT 
        fytoken, symbol_name, exchange_segment, instrument_id, expiry_timestamp,
        MIN(id) as keep_id,
        ARRAY_AGG(id ORDER BY id) as all_ids
    FROM nse_fo_raw
    WHERE fytoken IS NOT NULL 
      AND symbol_name IS NOT NULL 
      AND exchange_segment IS NOT NULL 
      AND instrument_id IS NOT NULL
    GROUP BY fytoken, symbol_name, exchange_segment, instrument_id, expiry_timestamp
    HAVING COUNT(*) > 1
)
DELETE FROM nse_fo_raw 
WHERE id IN (
    SELECT UNNEST(all_ids[2:]) 
    FROM duplicates
);

-- Step 3: Add unique constraints and indexes for nse_cm_raw
-- Drop existing index if it exists
DROP INDEX IF EXISTS idx_nse_cm_raw_unique;

-- Create unique index for nse_cm_raw
CREATE UNIQUE INDEX IF NOT EXISTS idx_nse_cm_raw_unique 
ON nse_cm_raw (fytoken, symbol_name, exchange_segment, instrument_id);

-- Create supporting indexes for nse_cm_raw
CREATE INDEX IF NOT EXISTS idx_nse_cm_raw_symbol_name ON nse_cm_raw (symbol_name);
CREATE INDEX IF NOT EXISTS idx_nse_cm_raw_fytoken ON nse_cm_raw (fytoken);

-- Step 4: Add unique constraints and indexes for nse_fo_raw
-- Drop existing index if it exists
DROP INDEX IF EXISTS idx_nse_fo_raw_unique;

-- Create unique index for nse_fo_raw
CREATE UNIQUE INDEX IF NOT EXISTS idx_nse_fo_raw_unique 
ON nse_fo_raw (fytoken, symbol_name, exchange_segment, instrument_id, expiry_timestamp);

-- Create supporting indexes for nse_fo_raw
CREATE INDEX IF NOT EXISTS idx_nse_fo_raw_symbol_name ON nse_fo_raw (symbol_name);
CREATE INDEX IF NOT EXISTS idx_nse_fo_raw_fytoken ON nse_fo_raw (fytoken);
CREATE INDEX IF NOT EXISTS idx_nse_fo_raw_expiry ON nse_fo_raw (expiry_timestamp);

-- Step 5: Verify the constraints work
-- Test insert of duplicate data (should be handled gracefully by application UPSERT logic)
-- This is just for verification - the application will handle duplicates with ON CONFLICT

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'NSE raw tables unique constraints migration completed successfully';
    RAISE NOTICE 'Tables now have unique constraints to prevent duplicate entries';
    RAISE NOTICE 'Application UPSERT logic will handle duplicates gracefully';
END $$;
