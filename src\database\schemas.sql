-- Robust Data Storage Service - Database Schema
-- Optimized for TimescaleDB with separate tables for different market segments

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- Create custom types
DO $$ BEGIN
    CREATE TYPE market_type AS ENUM ('EQUITY', 'INDEX', 'FUTURES', 'OPTIONS', 'COMMODITY', 'CURRENCY');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE option_type AS ENUM ('CE', 'PE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 1. Symbol master table
CREATE TABLE IF NOT EXISTS symbols (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    market_type market_type NOT NULL DEFAULT 'EQUITY',
    exchange VARCHAR(10) NOT NULL DEFAULT 'NSE',
    sector VARCHAR(100),
    industry VARCHAR(100),
    lot_size INTEGER DEFAULT 1,
    tick_size DECIMAL(10,4) DEFAULT 0.05,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Cash Market - Equities (NSE_CM.csv symbols)
-- Based on reference stock_ohlcv table structure with fyers_symbol column
CREATE TABLE IF NOT EXISTS equity_ohlcv (
    symbol text COLLATE pg_catalog."default" NOT NULL,
    exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
    "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
    datetime timestamp without time zone NOT NULL,
    open double precision NOT NULL,
    high double precision NOT NULL,
    low double precision NOT NULL,
    close double precision NOT NULL,
    volume bigint NOT NULL,
    fyers_symbol text COLLATE pg_catalog."default",
    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
    CONSTRAINT equity_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime)
);

-- 3. Cash Market - Indices (NSE_CM.csv indices)
-- Based on reference stock_ohlcv table structure with fyers_symbol column
CREATE TABLE IF NOT EXISTS index_ohlcv (
    symbol text COLLATE pg_catalog."default" NOT NULL,
    exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
    "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
    datetime timestamp without time zone NOT NULL,
    open double precision NOT NULL,
    high double precision NOT NULL,
    low double precision NOT NULL,
    close double precision NOT NULL,
    volume bigint NOT NULL,
    fyers_symbol text COLLATE pg_catalog."default",
    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
    CONSTRAINT index_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime)
);

-- 4. Futures contracts (NSE_FO.csv futures)
-- Based on reference stock_ohlcv table structure with additional futures-specific fields and fyers_symbol
CREATE TABLE IF NOT EXISTS futures_ohlcv (
    symbol text COLLATE pg_catalog."default" NOT NULL,
    exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
    "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
    datetime timestamp without time zone NOT NULL,
    open double precision NOT NULL,
    high double precision NOT NULL,
    low double precision NOT NULL,
    close double precision NOT NULL,
    volume bigint NOT NULL,
    expiry_date date NOT NULL,
    open_interest bigint DEFAULT 0,
    fyers_symbol text COLLATE pg_catalog."default",
    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
    CONSTRAINT futures_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime, expiry_date)
);

-- 5. Options contracts (NSE_FO.csv options)
-- Based on reference stock_ohlcv table structure with additional options-specific fields and fyers_symbol
CREATE TABLE IF NOT EXISTS options_ohlcv (
    symbol text COLLATE pg_catalog."default" NOT NULL,
    exchange text COLLATE pg_catalog."default" NOT NULL DEFAULT 'NSE'::text,
    "interval" text COLLATE pg_catalog."default" NOT NULL DEFAULT '1m'::text,
    datetime timestamp without time zone NOT NULL,
    open double precision NOT NULL,
    high double precision NOT NULL,
    low double precision NOT NULL,
    close double precision NOT NULL,
    volume bigint NOT NULL,
    expiry_date date NOT NULL,
    strike_price double precision NOT NULL,
    option_type option_type NOT NULL,
    open_interest bigint DEFAULT 0,
    fyers_symbol text COLLATE pg_catalog."default",
    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text),
    CONSTRAINT options_ohlcv_pkey PRIMARY KEY (symbol, "interval", datetime, expiry_date, strike_price, option_type)
);

-- 6. Data statistics and metadata tracking
CREATE TABLE IF NOT EXISTS data_statistics (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(100) NOT NULL,
    market_type market_type NOT NULL,
    exchange VARCHAR(10) NOT NULL DEFAULT 'NSE',
    first_timestamp TIMESTAMPTZ,
    last_timestamp TIMESTAMPTZ,
    total_records BIGINT DEFAULT 0,
    last_updated TIMESTAMPTZ DEFAULT NOW()
);

-- 7. Data resumption tracking for network failure handling
CREATE TABLE IF NOT EXISTS data_resumption (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(100) NOT NULL,
    market_type market_type NOT NULL,
    exchange VARCHAR(10) NOT NULL DEFAULT 'NSE',
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    last_fetched_date TIMESTAMPTZ,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. NSE_CM Raw Data Table (Cash Market - Equities and Indices)
CREATE TABLE IF NOT EXISTS nse_cm_raw (
    id SERIAL PRIMARY KEY,
    fytoken VARCHAR(50) NOT NULL,
    company_name VARCHAR(200),
    segment INTEGER,
    lot_size INTEGER,
    tick_size DECIMAL(10,4),
    isin VARCHAR(20),
    trading_hours VARCHAR(50),
    last_update_date DATE,
    expiry_timestamp BIGINT,
    symbol_name VARCHAR(100),
    exchange_segment INTEGER,
    exchange_instrument_id INTEGER,
    instrument_id INTEGER,
    symbol VARCHAR(100),
    token INTEGER,
    minimum_lot_size DECIMAL(12,4),
    instrument_type VARCHAR(10),
    underlying_fytoken VARCHAR(50),
    underlying_symbol VARCHAR(100),
    option_type INTEGER,
    strike_price DECIMAL(12,4),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 9. NSE_FO Raw Data Table (Futures and Options)
CREATE TABLE IF NOT EXISTS nse_fo_raw (
    id SERIAL PRIMARY KEY,
    fytoken VARCHAR(50) NOT NULL,
    company_name VARCHAR(200),
    segment INTEGER,
    lot_size INTEGER,
    tick_size DECIMAL(10,4),
    isin VARCHAR(20),
    trading_hours VARCHAR(50),
    last_update_date DATE,
    expiry_timestamp BIGINT,
    symbol_name VARCHAR(100),
    exchange_segment INTEGER,
    exchange_instrument_id INTEGER,
    instrument_id INTEGER,
    symbol VARCHAR(100),
    token INTEGER,
    minimum_lot_size DECIMAL(12,4),
    instrument_type VARCHAR(10),
    underlying_fytoken VARCHAR(50),
    underlying_symbol VARCHAR(100),
    option_type VARCHAR(10),
    strike_price DECIMAL(12,4),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 10. Symbol mapping between different formats
CREATE TABLE IF NOT EXISTS symbol_mapping (
    id SERIAL PRIMARY KEY,
    nse_symbol VARCHAR(100) NOT NULL,
    fyers_symbol VARCHAR(100) NOT NULL,
    market_type market_type NOT NULL,
    exchange VARCHAR(10) NOT NULL DEFAULT 'NSE',
    expiry_date DATE,
    strike_price DECIMAL(12,4),
    option_type option_type,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convert OHLCV tables to hypertables for time-series optimization
-- Only create hypertables if they don't already exist
DO $$
BEGIN
    -- Check and create hypertable for equity_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'equity_ohlcv'
    ) THEN
        PERFORM create_hypertable('equity_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
    END IF;

    -- Check and create hypertable for index_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'index_ohlcv'
    ) THEN
        PERFORM create_hypertable('index_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
    END IF;

    -- Check and create hypertable for futures_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'futures_ohlcv'
    ) THEN
        PERFORM create_hypertable('futures_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
    END IF;

    -- Check and create hypertable for options_ohlcv
    IF NOT EXISTS (
        SELECT 1 FROM timescaledb_information.hypertables
        WHERE hypertable_name = 'options_ohlcv'
    ) THEN
        PERFORM create_hypertable('options_ohlcv', 'datetime',
                                chunk_time_interval => INTERVAL '1 day',
                                if_not_exists => TRUE);
    END IF;
END $$;

-- Create optimized indexes for fast queries
-- Symbols table indexes
CREATE INDEX IF NOT EXISTS idx_symbols_symbol ON symbols (symbol);
CREATE INDEX IF NOT EXISTS idx_symbols_market_type ON symbols (market_type);
CREATE INDEX IF NOT EXISTS idx_symbols_exchange ON symbols (exchange);
CREATE INDEX IF NOT EXISTS idx_symbols_active ON symbols (is_active);

-- Equity OHLCV indexes
CREATE INDEX IF NOT EXISTS idx_equity_symbol_datetime ON equity_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_equity_datetime_desc ON equity_ohlcv (datetime DESC);
CREATE INDEX IF NOT EXISTS idx_equity_symbol_date_range ON equity_ohlcv (symbol, datetime) WHERE datetime >= NOW() - INTERVAL '1 year';
CREATE INDEX IF NOT EXISTS idx_equity_fyers_symbol ON equity_ohlcv (fyers_symbol);

-- Index OHLCV indexes
CREATE INDEX IF NOT EXISTS idx_index_symbol_datetime ON index_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_index_datetime_desc ON index_ohlcv (datetime DESC);
CREATE INDEX IF NOT EXISTS idx_index_symbol_date_range ON index_ohlcv (symbol, datetime) WHERE datetime >= NOW() - INTERVAL '1 year';
CREATE INDEX IF NOT EXISTS idx_index_fyers_symbol ON index_ohlcv (fyers_symbol);

-- Futures OHLCV indexes
CREATE INDEX IF NOT EXISTS idx_futures_symbol_datetime ON futures_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_futures_expiry_datetime ON futures_ohlcv (expiry_date, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_futures_datetime_desc ON futures_ohlcv (datetime DESC);
CREATE INDEX IF NOT EXISTS idx_futures_symbol_expiry ON futures_ohlcv (symbol, expiry_date);
CREATE INDEX IF NOT EXISTS idx_futures_fyers_symbol ON futures_ohlcv (fyers_symbol);

-- Options OHLCV indexes
CREATE INDEX IF NOT EXISTS idx_options_symbol_datetime ON options_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_options_expiry_strike ON options_ohlcv (expiry_date, strike_price, option_type);
CREATE INDEX IF NOT EXISTS idx_options_datetime_desc ON options_ohlcv (datetime DESC);
CREATE INDEX IF NOT EXISTS idx_options_symbol_expiry_strike ON options_ohlcv (symbol, expiry_date, strike_price, option_type);
CREATE INDEX IF NOT EXISTS idx_options_fyers_symbol ON options_ohlcv (fyers_symbol);

-- Data statistics indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_data_stats_unique ON data_statistics (symbol, market_type, exchange);
CREATE INDEX IF NOT EXISTS idx_data_stats_symbol ON data_statistics (symbol);
CREATE INDEX IF NOT EXISTS idx_data_stats_market_type ON data_statistics (market_type);

-- Data resumption indexes
CREATE INDEX IF NOT EXISTS idx_resumption_symbol_status ON data_resumption (symbol, status);
CREATE INDEX IF NOT EXISTS idx_resumption_status_updated ON data_resumption (status, updated_at);
CREATE INDEX IF NOT EXISTS idx_resumption_symbol_market ON data_resumption (symbol, market_type);

-- Symbol mapping indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_nse_symbol_mapping ON symbol_mapping (nse_symbol, market_type);
CREATE UNIQUE INDEX IF NOT EXISTS idx_fyers_symbol_mapping ON symbol_mapping (fyers_symbol);
CREATE INDEX IF NOT EXISTS idx_symbol_mapping_market_type ON symbol_mapping (market_type);
CREATE INDEX IF NOT EXISTS idx_symbol_mapping_active ON symbol_mapping (is_active);

-- NSE raw data unique constraints and indexes
-- For nse_cm_raw: Unique constraint on key fields to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_nse_cm_raw_unique ON nse_cm_raw (fytoken, symbol_name, exchange_segment, instrument_id);
CREATE INDEX IF NOT EXISTS idx_nse_cm_raw_symbol_name ON nse_cm_raw (symbol_name);
CREATE INDEX IF NOT EXISTS idx_nse_cm_raw_fytoken ON nse_cm_raw (fytoken);

-- For nse_fo_raw: Unique constraint on key fields including expiry_timestamp to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_nse_fo_raw_unique ON nse_fo_raw (fytoken, symbol_name, exchange_segment, instrument_id, expiry_timestamp);
CREATE INDEX IF NOT EXISTS idx_nse_fo_raw_symbol_name ON nse_fo_raw (symbol_name);
CREATE INDEX IF NOT EXISTS idx_nse_fo_raw_fytoken ON nse_fo_raw (fytoken);
CREATE INDEX IF NOT EXISTS idx_nse_fo_raw_expiry ON nse_fo_raw (expiry_timestamp);

-- Create compression policies for older data (optional, for production)
-- Compress data older than 7 days
-- SELECT add_compression_policy('equity_ohlcv', INTERVAL '7 days');
-- SELECT add_compression_policy('index_ohlcv', INTERVAL '7 days');
-- SELECT add_compression_policy('futures_ohlcv', INTERVAL '7 days');
-- SELECT add_compression_policy('options_ohlcv', INTERVAL '7 days');

-- Create retention policies for very old data (optional, for production)
-- Keep data for 5 years
-- SELECT add_retention_policy('equity_ohlcv', INTERVAL '5 years');
-- SELECT add_retention_policy('index_ohlcv', INTERVAL '5 years');
-- SELECT add_retention_policy('futures_ohlcv', INTERVAL '5 years');
-- SELECT add_retention_policy('options_ohlcv', INTERVAL '5 years');