#!/usr/bin/env python3
"""
Test script for the new functionality added to the data service.
Tests the exact symbols provided by the user.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.logging import setup_enhanced_logging, get_logger
from src.services.fyers_symbol_service import FyersSymbolService
from src.helpers.cli_operations import CLIOperations
from src.services.data_management_service import DataManagementService

# Initialize logging
setup_enhanced_logging()
logger = get_logger(__name__)


def test_fyers_symbol_construction():
    """Test Fyers symbol construction with exact examples."""
    logger.info("🧪 Testing Fyers symbol construction...")
    
    try:
        fyers_service = FyersSymbolService()
        
        # Test symbols provided by user
        test_cases = [
            {
                'description': 'EQUITY: NSE:RELIANCE-EQ',
                'expected': 'NSE:RELIANCE-EQ',
                'nse_symbol': 'RELIANCE',
                'market_type': 'EQUITY'
            },
            {
                'description': 'INDEX: NSE:NIFTY50-INDEX',
                'expected': 'NSE:NIFTY50-INDEX',
                'nse_symbol': 'NIFTY50',
                'market_type': 'INDEX'
            },
            {
                'description': 'FUTURES: NSE:RELIANCE25JULFUT',
                'expected': 'NSE:RELIANCE25JULFUT',
                'nse_symbol': 'RELIANCE',
                'market_type': 'FUTURES',
                'expiry_date': '2025-07-31'
            },
            {
                'description': 'OPTIONS: NSE:NIFTY25JUL25000CE',
                'expected': 'NSE:NIFTY25JUL25000CE',
                'nse_symbol': 'NIFTY',
                'market_type': 'OPTIONS',
                'expiry_date': '2025-07-31',
                'strike_price': 25000,
                'option_type': 'CE'
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"Testing: {test_case['description']}")
            
            from src.database.models import MarketType
            market_type_enum = MarketType(test_case['market_type'])
            
            constructed = fyers_service.construct_fyers_symbol(
                nse_symbol=test_case['nse_symbol'],
                market_type=market_type_enum,
                expiry_date=test_case.get('expiry_date'),
                strike_price=test_case.get('strike_price'),
                option_type=test_case.get('option_type')
            )
            
            if constructed == test_case['expected']:
                logger.info(f"  ✅ PASS: {constructed}")
            else:
                logger.error(f"  ❌ FAIL: Expected {test_case['expected']}, got {constructed}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in Fyers symbol construction test: {e}")
        return False


def test_cli_operations():
    """Test CLI operations functionality."""
    logger.info("🧪 Testing CLI operations...")
    
    try:
        cli_ops = CLIOperations()
        
        # Test fixing fyers symbols with examples
        logger.info("Testing fyers symbol fix...")
        success = cli_ops.fix_fyers_symbols_with_examples()
        
        if success:
            logger.info("✅ Fyers symbol fix test passed")
        else:
            logger.warning("⚠️ Fyers symbol fix test had issues")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in CLI operations test: {e}")
        return False


def test_data_management_service():
    """Test enhanced data management service."""
    logger.info("🧪 Testing enhanced data management service...")
    
    try:
        data_mgmt = DataManagementService()
        
        # Test table status checking
        tables_to_check = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv', 'symbol_mapping']
        
        for table in tables_to_check:
            status = data_mgmt.get_table_operations_status(table)
            logger.info(f"Table {table}: {status['status']} ({status['total_records']} records)")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in data management service test: {e}")
        return False


def test_symbol_validation():
    """Test symbol validation."""
    logger.info("🧪 Testing symbol validation...")
    
    try:
        fyers_service = FyersSymbolService()
        validation_results = fyers_service.validate_fyers_symbols()
        
        logger.info("📊 Validation Results:")
        logger.info(f"  Symbol mapping count: {validation_results['symbol_mapping_count']}")
        
        for table, info in validation_results['ohlcv_tables'].items():
            null_count = info['null_fyers_symbol']
            total_count = info['total_records']
            percentage = info['null_percentage']
            logger.info(f"  {table}: {null_count}/{total_count} null ({percentage:.1f}%)")
        
        validation_passed = validation_results['validation_passed']
        logger.info(f"Overall validation: {'✅ PASSED' if validation_passed else '❌ FAILED'}")
        
        return validation_passed
        
    except Exception as e:
        logger.error(f"Error in symbol validation test: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("🚀 Starting comprehensive functionality tests...")
    logger.info("=" * 80)
    
    test_results = {}
    
    # Run tests
    test_results['fyers_symbol_construction'] = test_fyers_symbol_construction()
    test_results['cli_operations'] = test_cli_operations()
    test_results['data_management_service'] = test_data_management_service()
    test_results['symbol_validation'] = test_symbol_validation()
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.warning("⚠️ Some tests failed. Check the logs above.")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error in test script: {e}")
        sys.exit(1)
