#!/usr/bin/env python3
"""
Analyze symbol discrepancies and generate detailed reports
"""

import sys
import pandas as pd
from pathlib import Path
from collections import defaultdict
import re

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.nse_symbol_processor import NSESymbolProcessor
from src.core.logging import get_logger
from src.database.connection import get_db
from datetime import datetime

logger = get_logger(__name__)

def analyze_csv_symbols():
    """Analyze symbols in NSE CSV files"""
    processor = NSESymbolProcessor()
    
    results = {
        'NSE_CM': {'total': 0, 'by_type': defaultdict(int), 'symbols': defaultdict(list)},
        'NSE_FO': {'total': 0, 'by_type': defaultdict(int), 'symbols': defaultdict(list)}
    }
    
    # Analyze NSE_CM.csv
    nse_cm_path = Path("NSE_CM.csv")
    if nse_cm_path.exists():
        logger.info("📊 Analyzing NSE_CM.csv...")
        df = processor._parse_csv_file(nse_cm_path, processor.nse_cm_columns)
        results['NSE_CM']['total'] = len(df)
        
        for _, row in df.iterrows():
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            if fyers_symbol.startswith('NSE:'):
                nse_symbol = fyers_symbol[4:]
                symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
                
                if symbol_info['market_type']:
                    market_type = symbol_info['market_type']
                    results['NSE_CM']['by_type'][market_type] += 1
                    results['NSE_CM']['symbols'][market_type].append(nse_symbol)
                else:
                    results['NSE_CM']['by_type']['SKIPPED'] += 1
                    results['NSE_CM']['symbols']['SKIPPED'].append(nse_symbol)
            else:
                results['NSE_CM']['by_type']['INVALID'] += 1
                results['NSE_CM']['symbols']['INVALID'].append(fyers_symbol)
    
    # Analyze NSE_FO.csv
    nse_fo_path = Path("NSE_FO.csv")
    if nse_fo_path.exists():
        logger.info("📊 Analyzing NSE_FO.csv...")
        df = processor._parse_csv_file(nse_fo_path, processor.nse_fo_columns)
        results['NSE_FO']['total'] = len(df)
        
        for _, row in df.iterrows():
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            if fyers_symbol.startswith('NSE:'):
                nse_symbol = fyers_symbol[4:]
                symbol_info = processor.helpers.extract_symbol_info(nse_symbol)
                
                if symbol_info['market_type']:
                    market_type = symbol_info['market_type']
                    results['NSE_FO']['by_type'][market_type] += 1
                    results['NSE_FO']['symbols'][market_type].append(nse_symbol)
                else:
                    results['NSE_FO']['by_type']['SKIPPED'] += 1
                    results['NSE_FO']['symbols']['SKIPPED'].append(nse_symbol)
            else:
                results['NSE_FO']['by_type']['INVALID'] += 1
                results['NSE_FO']['symbols']['INVALID'].append(fyers_symbol)
    
    return results

def analyze_database_symbols():
    """Analyze symbols in database"""
    try:
        db = get_db()
        
        # Get symbol mapping counts
        symbol_mapping_query = """
            SELECT market_type, COUNT(*) as count
            FROM symbol_mapping
            WHERE is_active = TRUE
            GROUP BY market_type
            ORDER BY market_type
        """
        
        mapping_results = db.execute_query(symbol_mapping_query)
        
        # Get OHLCV table counts
        ohlcv_queries = {
            'equity_ohlcv': "SELECT COUNT(DISTINCT symbol) as count FROM equity_ohlcv",
            'index_ohlcv': "SELECT COUNT(DISTINCT symbol) as count FROM index_ohlcv", 
            'futures_ohlcv': "SELECT COUNT(DISTINCT symbol) as count FROM futures_ohlcv",
            'options_ohlcv': "SELECT COUNT(DISTINCT symbol) as count FROM options_ohlcv"
        }
        
        ohlcv_results = {}
        for table, query in ohlcv_queries.items():
            result = db.execute_query(query)
            ohlcv_results[table] = result[0]['count'] if result else 0
        
        # Get null fyers_symbol counts
        null_fyers_queries = {
            'equity_ohlcv': "SELECT COUNT(*) as count FROM equity_ohlcv WHERE fyers_symbol IS NULL",
            'index_ohlcv': "SELECT COUNT(*) as count FROM index_ohlcv WHERE fyers_symbol IS NULL",
            'futures_ohlcv': "SELECT COUNT(*) as count FROM futures_ohlcv WHERE fyers_symbol IS NULL",
            'options_ohlcv': "SELECT COUNT(*) as count FROM options_ohlcv WHERE fyers_symbol IS NULL"
        }
        
        null_fyers_results = {}
        for table, query in null_fyers_queries.items():
            result = db.execute_query(query)
            null_fyers_results[table] = result[0]['count'] if result else 0
        
        return {
            'symbol_mapping': {row['market_type']: row['count'] for row in mapping_results},
            'ohlcv_tables': ohlcv_results,
            'null_fyers_symbols': null_fyers_results
        }
        
    except Exception as e:
        logger.error(f"Error analyzing database: {e}")
        return {}

def generate_discrepancy_report(csv_results, db_results):
    """Generate detailed discrepancy report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = Path("logs") / f"symbol_analysis_report_{timestamp}.txt"
    
    # Ensure logs directory exists
    report_file.parent.mkdir(exist_ok=True)
    
    with open(report_file, 'w') as f:
        f.write("SYMBOL ANALYSIS REPORT\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {datetime.now()}\n\n")
        
        # CSV Analysis
        f.write("CSV FILE ANALYSIS\n")
        f.write("-" * 40 + "\n")
        
        total_equity = csv_results['NSE_CM']['by_type']['EQUITY'] + csv_results['NSE_FO']['by_type']['EQUITY']
        total_index = csv_results['NSE_CM']['by_type']['INDEX'] + csv_results['NSE_FO']['by_type']['INDEX']
        total_futures = csv_results['NSE_CM']['by_type']['FUTURES'] + csv_results['NSE_FO']['by_type']['FUTURES']
        total_options = csv_results['NSE_CM']['by_type']['OPTIONS'] + csv_results['NSE_FO']['by_type']['OPTIONS']
        
        f.write(f"NSE_CM.csv Total Rows: {csv_results['NSE_CM']['total']:,}\n")
        f.write(f"NSE_FO.csv Total Rows: {csv_results['NSE_FO']['total']:,}\n\n")
        
        f.write("Symbol Counts by Type (Combined):\n")
        f.write(f"  EQUITY: {total_equity:,}\n")
        f.write(f"  INDEX: {total_index:,}\n")
        f.write(f"  FUTURES: {total_futures:,}\n")
        f.write(f"  OPTIONS: {total_options:,}\n\n")
        
        f.write("Symbol Counts by File:\n")
        f.write("NSE_CM.csv:\n")
        for market_type, count in csv_results['NSE_CM']['by_type'].items():
            f.write(f"  {market_type}: {count:,}\n")
        
        f.write("NSE_FO.csv:\n")
        for market_type, count in csv_results['NSE_FO']['by_type'].items():
            f.write(f"  {market_type}: {count:,}\n")
        
        # Database Analysis
        if db_results:
            f.write("\nDATABASE ANALYSIS\n")
            f.write("-" * 40 + "\n")
            
            f.write("Symbol Mapping Table:\n")
            for market_type, count in db_results['symbol_mapping'].items():
                f.write(f"  {market_type}: {count:,}\n")
            
            f.write("\nOHLCV Tables (Distinct Symbols):\n")
            for table, count in db_results['ohlcv_tables'].items():
                f.write(f"  {table}: {count:,}\n")
            
            f.write("\nNull Fyers Symbol Counts:\n")
            for table, count in db_results['null_fyers_symbols'].items():
                f.write(f"  {table}: {count:,}\n")
        
        # Discrepancy Analysis
        f.write("\nDISCREPANCY ANALYSIS\n")
        f.write("-" * 40 + "\n")
        
        expected_counts = {
            'EQUITY': total_equity,
            'INDEX': total_index,
            'FUTURES': total_futures,
            'OPTIONS': total_options
        }
        
        if db_results and 'symbol_mapping' in db_results:
            f.write("Expected vs Actual (Symbol Mapping):\n")
            for market_type, expected in expected_counts.items():
                actual = db_results['symbol_mapping'].get(market_type, 0)
                diff = expected - actual
                status = "✅" if diff == 0 else "❌"
                f.write(f"  {market_type}: Expected {expected:,}, Actual {actual:,}, Diff {diff:+,} {status}\n")
        
        # Sample skipped symbols
        f.write("\nSAMPLE SKIPPED SYMBOLS\n")
        f.write("-" * 40 + "\n")
        
        for file_type in ['NSE_CM', 'NSE_FO']:
            skipped = csv_results[file_type]['symbols']['SKIPPED'][:20]  # First 20
            if skipped:
                f.write(f"{file_type} Skipped Symbols (first 20):\n")
                for i, symbol in enumerate(skipped, 1):
                    f.write(f"  {i:2d}. {symbol}\n")
                f.write(f"  ... and {len(csv_results[file_type]['symbols']['SKIPPED']) - 20} more\n\n")
    
    logger.info(f"📄 Detailed report written to: {report_file}")
    return report_file

if __name__ == "__main__":
    logger.info("🔍 Starting comprehensive symbol analysis...")
    
    try:
        # Analyze CSV files
        logger.info("📊 Analyzing CSV files...")
        csv_results = analyze_csv_symbols()
        
        # Analyze database
        logger.info("🗄️ Analyzing database...")
        db_results = analyze_database_symbols()
        
        # Generate report
        logger.info("📄 Generating discrepancy report...")
        report_file = generate_discrepancy_report(csv_results, db_results)
        
        # Print summary
        logger.info("\n📊 SUMMARY:")
        total_equity = csv_results['NSE_CM']['by_type']['EQUITY'] + csv_results['NSE_FO']['by_type']['EQUITY']
        total_index = csv_results['NSE_CM']['by_type']['INDEX'] + csv_results['NSE_FO']['by_type']['INDEX']
        total_futures = csv_results['NSE_CM']['by_type']['FUTURES'] + csv_results['NSE_FO']['by_type']['FUTURES']
        total_options = csv_results['NSE_CM']['by_type']['OPTIONS'] + csv_results['NSE_FO']['by_type']['OPTIONS']
        
        logger.info(f"CSV Analysis - Total Symbols Found:")
        logger.info(f"  EQUITY: {total_equity:,}")
        logger.info(f"  INDEX: {total_index:,}")
        logger.info(f"  FUTURES: {total_futures:,}")
        logger.info(f"  OPTIONS: {total_options:,}")
        
        if db_results and 'symbol_mapping' in db_results:
            logger.info(f"\nDatabase Symbol Mapping:")
            for market_type, count in db_results['symbol_mapping'].items():
                logger.info(f"  {market_type}: {count:,}")

            # Add percentage validation output
            logger.info(f"\n📊 Market Type Validation Results:")
            expected_counts = {
                'EQUITY': total_equity,
                'INDEX': total_index,
                'FUTURES': total_futures,
                'OPTIONS': total_options
            }

            for market_type, expected in expected_counts.items():
                actual = db_results['symbol_mapping'].get(market_type, 0)
                if expected > 0:
                    percentage = (actual / expected) * 100
                    logger.info(f"  {market_type}\t{expected:,}\t{actual:,}\t{percentage:.1f}%")
                else:
                    logger.info(f"  {market_type}\t{expected:,}\t{actual:,}\tN/A")

        logger.info(f"\n✅ Analysis completed! Full report: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        sys.exit(1)
