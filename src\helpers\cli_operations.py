"""
CLI Operations Helper - Handles command line operations for main.py
"""

import logging
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta

from src.database.models import MarketType
from src.services.bulk_data_service import BulkDataService
from src.services.fyers_symbol_service import FyersSymbolService
from src.core.symbol_classifier import SymbolClassifier
from src.core.nse_symbol_processor import NSESymbolProcessor

logger = logging.getLogger(__name__)


class CLIOperations:
    """Helper class for command line operations."""
    
    def __init__(self):
        """Initialize CLI operations."""
        self.fyers_symbol_service = FyersSymbolService()
        self.symbol_classifier = SymbolClassifier()
        self.nse_processor = NSESymbolProcessor()
    
    def fetch_specific_symbol_data(self, symbol: str, market_type: str, days: int = 1) -> bool:
        """
        Fetch data for a specific symbol with exact Fyers symbol format.
        
        Args:
            symbol: Exact Fyers symbol (e.g., "NSE:RELIANCE-EQ")
            market_type: Market type string
            days: Number of days of data to fetch
            
        Returns:
            Success status
        """
        try:
            logger.info(f"🔄 Fetching {days} day(s) of data for {symbol} ({market_type})")
            
            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())
            
            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: [symbol]}
            
            # Use bulk service to fetch data
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=days
            ))
            
            # Check results
            if market_type_enum in results:
                symbol_results = results[market_type_enum]
                success = symbol_results.get(symbol, False)
                
                if success:
                    logger.info(f"✅ Successfully fetched data for {symbol}")
                    return True
                else:
                    logger.error(f"❌ Failed to fetch data for {symbol}")
                    return False
            else:
                logger.error(f"❌ No results for market type {market_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return False
    
    def fetch_multiple_symbols_data(self, symbols: List[str], market_type: str, days: int = 1) -> Dict[str, bool]:
        """
        Fetch data for multiple symbols of the same market type.
        
        Args:
            symbols: List of exact Fyers symbols
            market_type: Market type string
            days: Number of days of data to fetch
            
        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching {days} day(s) of data for {len(symbols)} {market_type} symbols")
            
            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())
            
            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: symbols}
            
            # Use bulk service to fetch data
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=days
            ))
            
            # Extract results for the market type
            if market_type_enum in results:
                return results[market_type_enum]
            else:
                return {symbol: False for symbol in symbols}
                
        except Exception as e:
            logger.error(f"Error fetching data for multiple symbols: {e}")
            return {symbol: False for symbol in symbols}
    
    def fetch_all_symbols_from_mapping(self, market_type: str, days: int = 1, limit: Optional[int] = None) -> Dict[str, bool]:
        """
        Fetch data for all symbols of a market type from symbol_mapping table.
        
        Args:
            market_type: Market type string
            days: Number of days of data to fetch
            limit: Optional limit on number of symbols to process
            
        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching data for all {market_type} symbols from symbol_mapping table")
            
            # Get symbols from symbol_mapping table
            symbols = self._get_symbols_from_mapping(market_type, limit)
            
            if not symbols:
                logger.warning(f"No symbols found for market type {market_type}")
                return {}
            
            logger.info(f"Found {len(symbols)} symbols to process")
            
            # Fetch data for all symbols
            return self.fetch_multiple_symbols_data(symbols, market_type, days)
            
        except Exception as e:
            logger.error(f"Error fetching data for all symbols: {e}")
            return {}
    
    def _get_symbols_from_mapping(self, market_type: str, limit: Optional[int] = None) -> List[str]:
        """Get Fyers symbols from symbol_mapping table."""
        try:
            from src.database.connection import get_db
            from src.database.models import SymbolMapping, MarketType
            from sqlalchemy import and_
            
            db = next(get_db())
            
            try:
                market_type_enum = MarketType(market_type.upper())
                
                query = db.query(SymbolMapping).filter(
                    and_(
                        SymbolMapping.market_type == market_type_enum,
                        SymbolMapping.is_active == True,
                        SymbolMapping.fyers_symbol.isnot(None)
                    )
                )
                
                if limit:
                    query = query.limit(limit)
                
                mappings = query.all()
                return [mapping.fyers_symbol for mapping in mappings]
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting symbols from mapping: {e}")
            return []
    
    def fix_fyers_symbols_with_examples(self) -> bool:
        """Fix fyers_symbol columns using the exact examples provided by user."""
        try:
            logger.info("🔧 Fixing fyers_symbol columns with exact examples")
            
            # Exact symbols provided by user
            test_symbols = {
                'EQUITY': 'NSE:RELIANCE-EQ',
                'INDEX': 'NSE:NIFTY50-INDEX', 
                'FUTURES': 'NSE:RELIANCE25JULFUT',
                'OPTIONS': 'NSE:NIFTY25JUL25000CE'
            }
            
            # Fix specific symbols first
            fix_results = self.fyers_symbol_service.fix_specific_symbols(test_symbols)
            
            # Update all OHLCV tables
            update_results = self.fyers_symbol_service.update_fyers_symbols_in_ohlcv_tables()
            
            # Validate results
            validation_results = self.fyers_symbol_service.validate_fyers_symbols()
            
            # Log results
            logger.info("📊 Fix Results:")
            for market_type, success in fix_results.items():
                status = "✅" if success else "❌"
                logger.info(f"  {status} {market_type}: {test_symbols[market_type]}")
            
            logger.info("📊 Update Results:")
            for table, count in update_results.items():
                logger.info(f"  {table}: {count} rows updated")
            
            logger.info("📊 Validation Results:")
            for table, info in validation_results['ohlcv_tables'].items():
                null_count = info['null_fyers_symbol']
                total_count = info['total_records']
                logger.info(f"  {table}: {null_count}/{total_count} null fyers_symbol")
            
            return validation_results['validation_passed']
            
        except Exception as e:
            logger.error(f"Error fixing fyers_symbols: {e}")
            return False
    
    def process_symbols_with_resume(self, market_type: str, days: int = 1, 
                                   batch_size: int = 10, start_from: int = 0) -> Dict[str, Any]:
        """
        Process symbols with resume capability and error handling.
        
        Args:
            market_type: Market type to process
            days: Number of days of data to fetch
            batch_size: Number of symbols to process in each batch
            start_from: Index to start processing from (for resume)
            
        Returns:
            Processing results with resume information
        """
        try:
            logger.info(f"🔄 Processing {market_type} symbols with resume capability")
            
            # Get all symbols for the market type
            all_symbols = self._get_symbols_from_mapping(market_type)
            
            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return {'success': False, 'error': 'No symbols found'}
            
            total_symbols = len(all_symbols)
            logger.info(f"Found {total_symbols} symbols to process")
            
            # Start from specified index
            symbols_to_process = all_symbols[start_from:]
            logger.info(f"Starting from index {start_from}, processing {len(symbols_to_process)} symbols")
            
            results = {
                'total_symbols': total_symbols,
                'processed_symbols': 0,
                'successful_symbols': 0,
                'failed_symbols': 0,
                'last_processed_index': start_from - 1,
                'symbol_results': {},
                'success': True
            }
            
            # Process in batches
            for i in range(0, len(symbols_to_process), batch_size):
                batch = symbols_to_process[i:i + batch_size]
                batch_start_index = start_from + i
                
                logger.info(f"Processing batch {i//batch_size + 1}: symbols {batch_start_index} to {batch_start_index + len(batch) - 1}")
                
                try:
                    # Process batch
                    batch_results = self.fetch_multiple_symbols_data(batch, market_type, days)
                    
                    # Update results
                    for symbol, success in batch_results.items():
                        results['symbol_results'][symbol] = success
                        results['processed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + batch.index(symbol)
                        
                        if success:
                            results['successful_symbols'] += 1
                        else:
                            results['failed_symbols'] += 1
                    
                    # Log batch progress
                    batch_success_count = sum(1 for success in batch_results.values() if success)
                    logger.info(f"Batch completed: {batch_success_count}/{len(batch)} successful")
                    
                except Exception as e:
                    logger.error(f"Error processing batch starting at {batch_start_index}: {e}")
                    # Mark all symbols in batch as failed
                    for symbol in batch:
                        results['symbol_results'][symbol] = False
                        results['processed_symbols'] += 1
                        results['failed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + batch.index(symbol)
            
            # Final summary
            success_rate = (results['successful_symbols'] / results['processed_symbols'] * 100) if results['processed_symbols'] > 0 else 0
            logger.info(f"📊 Processing Summary:")
            logger.info(f"  Total symbols: {results['total_symbols']}")
            logger.info(f"  Processed: {results['processed_symbols']}")
            logger.info(f"  Successful: {results['successful_symbols']}")
            logger.info(f"  Failed: {results['failed_symbols']}")
            logger.info(f"  Success rate: {success_rate:.1f}%")
            logger.info(f"  Last processed index: {results['last_processed_index']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in process_symbols_with_resume: {e}")
            return {'success': False, 'error': str(e)}
