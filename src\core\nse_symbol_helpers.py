"""
NSE Symbol Processing Helper Utilities.
Contains helper functions and utilities for NSE symbol processing to reduce
the main processor file size and improve maintainability.
"""

import re
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from pathlib import Path

logger = logging.getLogger(__name__)


class NSESymbolHelpers:
    """Helper utilities for NSE symbol processing."""
    
    def __init__(self):
        """Initialize helper utilities."""
        # Strict symbol patterns for filtering based on the 5 specified patterns
        # EQUITY pattern: UNDERLYING-EQ (e.g., RELIANCE-EQ)
        # INDEX pattern: UNDERLYING-INDEX (e.g., NIFTY50-INDEX)
        # FUTURES pattern: UNDERLYINGYYMMMFUT (e.g., R<PERSON><PERSON><PERSON><PERSON>25JULFUT)
        # OPTIONS monthly pattern: UNDERLYINGYYMMMSTRIKECE/PE (e.g., RELIANCE25JUL2500CE)
        # OPTIONS WEEKLY pattern: UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE)
        #   Format: UNDERLYING + YY + MDD + STRIKE + CE/PE where MDD is month(1 digit) + day(2 digits)
        #   Common underlyings: NIFTY, BANKNIFTY, FINNIFTY, etc.
        self.patterns = {
            'EQUITY': re.compile(r'^[A-Z0-9&\-]+\-EQ$'),
            'INDEX': re.compile(r'^[A-Z0-9&\-]+\-INDEX$'),  # Updated to include & for consistency
            'FUTURES': re.compile(r'^[A-Z0-9&\-]+\d{2}[A-Z]{3}FUT$'),
            'OPTIONS_MONTHLY': re.compile(r'^[A-Z0-9&\-]+\d{2}[A-Z]{3}\d+(?:\.\d+)?(CE|PE)$'),
            'OPTIONS_WEEKLY': re.compile(r'^[A-Z0-9&\-]+\d{2}[1-9]\d{2}\d+(?:\.\d+)?(CE|PE)$')
        }
    
    def extract_symbol_info(self, symbol: str) -> Dict[str, any]:
        """Extract market type and other info from symbol using strict pattern matching."""
        symbol_info = {
            'market_type': None,
            'underlying': None,
            'expiry_date': None,
            'strike_price': None,
            'option_type': None
        }

        # Check patterns in order of specificity (most specific first)

        # Check EQUITY pattern: UNDERLYING-EQ (e.g., RELIANCE-EQ)
        if self.patterns['EQUITY'].match(symbol):
            symbol_info['market_type'] = 'EQUITY'
            symbol_info['underlying'] = symbol.replace('-EQ', '')

        # Check INDEX pattern: UNDERLYING-INDEX (e.g., NIFTY50-INDEX)
        elif self.patterns['INDEX'].match(symbol):
            symbol_info['market_type'] = 'INDEX'
            symbol_info['underlying'] = symbol.replace('-INDEX', '')

        # Check FUTURES pattern: UNDERLYINGYYMMMFUT (e.g., RELIANCE25JULFUT, M&M25JULFUT, BAJAJ-AUTO25JULFUT)
        elif self.patterns['FUTURES'].match(symbol):
            symbol_info['market_type'] = 'FUTURES'
            # Extract underlying and expiry from UNDERLYINGYYMMMFUT (handle &, - characters)
            match = re.match(r'^([A-Z0-9&\-]+)(\d{2})([A-Z]{3})FUT$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_str = match.group(3)
                symbol_info['expiry_date'] = self.parse_expiry_date(year, month_str)

        # Check OPTIONS monthly pattern: UNDERLYINGYYMMMSTRIKECE/PE (e.g., RELIANCE25JUL2500CE, M&M25JUL2500CE, BAJAJ-AUTO25JUL5900CE)
        elif self.patterns['OPTIONS_MONTHLY'].match(symbol):
            symbol_info['market_type'] = 'OPTIONS'
            # Extract from UNDERLYINGYYMMMSTRIKECE/PE (handle &, - characters)
            match = re.match(r'^([A-Z0-9&\-]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_str = match.group(3)
                symbol_info['expiry_date'] = self.parse_expiry_date(year, month_str)
                symbol_info['strike_price'] = float(match.group(4))
                symbol_info['option_type'] = match.group(5)

        # Check OPTIONS weekly pattern: UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE)
        # Format: UNDERLYING + YY + MDD + STRIKE + CE/PE where MDD is month(1 digit) + day(2 digits)
        elif self.patterns['OPTIONS_WEEKLY'].match(symbol):
            symbol_info['market_type'] = 'OPTIONS'
            # Extract from UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE, handle &, - characters)
            match = re.match(r'^([A-Z0-9&\-]+)(\d{2})([1-9]\d{2})(\d+(?:\.\d+)?)(CE|PE)$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                mdd = match.group(3)  # month(1 digit) + day(2 digits)
                month_digit = mdd[0]  # First digit is month
                day = int(mdd[1:3])   # Next two digits are day
                symbol_info['expiry_date'] = self.parse_weekly_expiry_date(year, month_digit, day)
                symbol_info['strike_price'] = float(match.group(4))
                symbol_info['option_type'] = match.group(5)

        # If no pattern matched, return None for market_type
        if symbol_info['market_type'] is None:
            return symbol_info

        # Fallback patterns for symbols that might not match exact patterns
        elif symbol.endswith('-EQ'):
            symbol_info['market_type'] = 'EQUITY'
            symbol_info['underlying'] = symbol.replace('-EQ', '')
        elif symbol.endswith('-INDEX'):
            symbol_info['market_type'] = 'INDEX'
            symbol_info['underlying'] = symbol.replace('-INDEX', '')
        elif 'FUT' in symbol and not symbol.endswith('-EQ') and not symbol.endswith('-INDEX'):
            symbol_info['market_type'] = 'FUTURES'
            # Basic underlying extraction for futures
            if symbol.endswith('FUT'):
                # Try to extract underlying from UNDERLYINGFUT pattern
                underlying_match = re.match(r'^([A-Z0-9&\-]+?)(?:\d{2}[A-Z]{3})?FUT$', symbol)
                if underlying_match:
                    symbol_info['underlying'] = underlying_match.group(1)
        elif ('CE' in symbol or 'PE' in symbol) and not symbol.endswith('-EQ') and not symbol.endswith('-INDEX'):
            symbol_info['market_type'] = 'OPTIONS'
            # Basic underlying extraction for options
            option_match = re.match(r'^([A-Z0-9&\-]+?)(?:\d+)?(?:CE|PE)$', symbol)
            if option_match:
                symbol_info['underlying'] = option_match.group(1)
                symbol_info['option_type'] = 'CE' if 'CE' in symbol else 'PE'

        return symbol_info
    
    def parse_expiry_date(self, year: int, month_str: str) -> Optional[datetime]:
        """Parse expiry date from year and month string."""
        month_map = {
            'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
            'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
        }
        
        try:
            month = month_map.get(month_str)
            if month:
                # Last Thursday of the month (typical expiry)
                last_day = datetime(year, month + 1, 1) - timedelta(days=1) if month < 12 else datetime(year, 12, 31)
                while last_day.weekday() != 3:  # Thursday is 3
                    last_day -= timedelta(days=1)
                return last_day
        except Exception as e:
            logger.warning(f"Error parsing expiry date {year}-{month_str}: {e}")
        
        return None
    
    def parse_weekly_expiry_date(self, year: int, month_digit: str, day: int) -> Optional[datetime]:
        """Parse weekly expiry date from year, month digit, and day with validation."""
        # Month digit mapping (1-9, O, N, D for Oct, Nov, Dec)
        month_map = {
            '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6,
            '7': 7, '8': 8, '9': 9, 'O': 10, 'N': 11, 'D': 12
        }

        try:
            month = month_map.get(month_digit)
            if month and 1 <= day <= 31:
                # Validate the date is actually valid for the month
                try:
                    return datetime(year, month, day)
                except ValueError:
                    # Invalid date (e.g., Feb 30), skip it
                    return None
        except Exception as e:
            logger.debug(f"Error parsing weekly expiry date {year}-{month_digit}-{day}: {e}")

        return None

    def write_failed_rows_to_file(self, logs_dir: Path, table_name: str, failed_rows: List[Dict]) -> None:
        """Write failed rows to a text file in the logs directory."""
        if not failed_rows:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = logs_dir / f"{table_name}_failed_rows_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Failed rows for {table_name} - {datetime.now()}\n")
                f.write("=" * 80 + "\n\n")

                for i, failed_row in enumerate(failed_rows, 1):
                    f.write(f"Failed Row #{i}:\n")
                    f.write(f"  Row Index: {failed_row['row_index']}\n")
                    f.write(f"  Attempt: {failed_row['attempt']}\n")
                    f.write(f"  Error: {failed_row['error']}\n")
                    f.write(f"  Data: {failed_row['data']}\n")
                    f.write("-" * 40 + "\n")

                f.write(f"\nTotal failed rows: {len(failed_rows)}\n")

            logger.info(f"Failed rows written to: {filename}")

        except Exception as e:
            logger.error(f"Error writing failed rows to file: {e}")

    def write_skipped_symbols(self, logs_dir: Path, skipped_symbols: List[str], market_segment: str) -> None:
        """Write skipped symbols to a text file for analysis."""
        if not skipped_symbols:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = logs_dir / f"skipped_symbols_{market_segment}_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Skipped symbols for {market_segment} - {datetime.now()}\n")
                f.write("=" * 80 + "\n\n")
                f.write(f"Total skipped symbols: {len(skipped_symbols)}\n\n")

                for i, symbol in enumerate(skipped_symbols, 1):
                    f.write(f"{i:4d}. {symbol}\n")

            logger.info(f"Skipped symbols written to: {filename}")

        except Exception as e:
            logger.error(f"Error writing skipped symbols to file: {e}")

    def filter_relevant_symbols(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Strictly filter symbols based on the 5 specified patterns only.
        EQUITY pattern: UNDERLYING-EQ (e.g., RELIANCE-EQ)
        INDEX pattern: UNDERLYING-INDEX (e.g., NIFTY50-INDEX)
        FUTURES pattern: UNDERLYINGYYMMMFUT (e.g., RELIANCE25JULFUT)
        OPTIONS monthly pattern: UNDERLYINGYYMMMSTRIKECE/PE (e.g., RELIANCE25JUL2500CE)
        OPTIONS WEEKLY pattern: UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE)
        Omit all symbols that don't match these exact patterns.
        """
        if df.empty:
            return df

        filtered_rows = []
        skipped_symbols = []
        symbol_counts = {
            'EQUITY': 0,
            'INDEX': 0,
            'FUTURES': 0,
            'OPTIONS': 0,
            'SKIPPED': 0
        }

        logger.info("Starting strict symbol filtering based on 5 specified patterns")

        for _, row in df.iterrows():
            # Extract NSE symbol from Fyers symbol format
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            if fyers_symbol.startswith('NSE:'):
                nse_symbol = fyers_symbol[4:]  # Remove 'NSE:' prefix
            else:
                skipped_symbols.append(fyers_symbol)
                symbol_counts['SKIPPED'] += 1
                continue

            symbol_info = self.extract_symbol_info(nse_symbol)

            # Only include symbols that match our strict patterns
            if symbol_info['market_type'] in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
                # Add symbol info to row
                row_dict = row.to_dict()
                row_dict.update(symbol_info)
                row_dict['nse_symbol'] = nse_symbol  # Store NSE symbol separately
                filtered_rows.append(row_dict)
                symbol_counts[symbol_info['market_type']] += 1
            else:
                skipped_symbols.append(nse_symbol)
                symbol_counts['SKIPPED'] += 1

        # Log strict filtering results
        logger.info(f"Strict filtering results: {symbol_counts}")

        total_processed = len(df)
        total_filtered = sum(symbol_counts[mt] for mt in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'])
        logger.info(f"Total symbols processed: {total_processed:,}")
        logger.info(f"Total symbols matching strict patterns: {total_filtered:,}")
        logger.info(f"Total symbols skipped (not matching patterns): {symbol_counts['SKIPPED']:,}")

        if total_processed > 0:
            success_rate = (total_filtered / total_processed) * 100
            logger.info(f"Strict filtering success rate: {success_rate:.1f}% ({total_filtered}/{total_processed})")

            # Log detailed breakdown by market type
            for market_type in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
                count = symbol_counts[market_type]
                if count > 0:
                    percentage = (count / total_processed) * 100
                    logger.info(f"  {market_type}: {count:,} symbols ({percentage:.1f}%)")

        # Write skipped symbols to file for analysis
        if skipped_symbols:
            # Try to determine market segment from DataFrame context or default to "UNKNOWN"
            market_segment = "UNKNOWN"
            # Note: This will need logs_dir passed from the main processor
            # self.write_skipped_symbols(logs_dir, skipped_symbols, market_segment)

            # Log sample of skipped symbols for debugging
            if len(skipped_symbols) > 0:
                logger.warning(f"Skipped {len(skipped_symbols)} symbols. Sample: {skipped_symbols[:5]}")

        if filtered_rows:
            filtered_df = pd.DataFrame(filtered_rows)
            logger.info(f"Filtered {len(filtered_df)} relevant symbols from {len(df)} total symbols")
            return filtered_df
        else:
            logger.warning("No relevant symbols found after filtering")
            return pd.DataFrame()

    def get_symbols_not_loaded(self, failed_rows: Dict[str, List]) -> Dict[str, Set[str]]:
        """Get list of symbols that were not loaded successfully."""
        not_loaded = {
            'EQUITY': set(),
            'INDEX': set(),
            'FUTURES': set(),
            'OPTIONS': set()
        }

        # Extract symbols from failed rows
        for failed_row in failed_rows['nse_cm'] + failed_rows['nse_fo']:
            try:
                symbol_name = failed_row['data'].get('symbol_name', '')
                if ':' in symbol_name:
                    symbol = symbol_name.split(':')[-1]
                else:
                    symbol = symbol_name

                symbol_info = self.extract_symbol_info(symbol)
                market_type = symbol_info.get('market_type')

                if market_type and market_type in not_loaded:
                    not_loaded[market_type].add(symbol)

            except Exception as e:
                logger.debug(f"Error extracting symbol from failed row: {e}")

        return not_loaded
